# Task 3 Data Processing Pipeline

This document describes the complete data processing pipeline for Task 3 accelerometer data, which processes raw sensor data through multiple stages to create various datasets for machine learning experiments.

## Overview

The pipeline processes accelerometer data collected from mobile devices through the following stages:

1. **unprocessed** → **processed**: Downsample from variable 190-210Hz to fixed 50Hz
2. **processed** → **processed_trimmed**: Remove noise by trimming first 4.5s and last 0.5s
3. **processed_trimmed** → **raw_dataset**: Organize data for train/test splitting
4. **raw_dataset**: Apply leave-one-subject-out strategy (Subjects 9-12 to test)
5. **raw_dataset** → **TSFEL_features**: Extract 1173 time-series features using TSFEL
6. **TSFEL_features** → **TSFEL_dataset**: Organize features with train/test splits
7. **TSFEL_dataset** → **TSFEL_filtereddataset**: Apply ANOVA F-test feature selection

## Prerequisites

Make sure you have the required dependencies installed:

```bash
pip install pandas numpy tsfel pathlib
```

## Usage

### Run Complete Pipeline

To process all data from unprocessed to final filtered features:

```bash
python task3_data_processing_pipeline.py
```

### Run with Custom Base Directory

If your task3_data directory is in a different location:

```bash
python task3_data_processing_pipeline.py --base_dir "path/to/your/task3_data"
```

### Run Individual Steps

You can run individual steps of the pipeline:

```bash
# Step 1: Downsample to 50Hz
python task3_data_processing_pipeline.py --step 1

# Step 2: Trim data
python task3_data_processing_pipeline.py --step 2

# Step 3: Create raw dataset structure
python task3_data_processing_pipeline.py --step 3

# Step 4: Train/test split
python task3_data_processing_pipeline.py --step 4

# Step 5: Extract TSFEL features
python task3_data_processing_pipeline.py --step 5

# Step 6: Create TSFEL dataset with splits
python task3_data_processing_pipeline.py --step 6

# Step 7: Filter features
python task3_data_processing_pipeline.py --step 7
```

## Input Data Structure

The pipeline expects the following input structure in your base directory:

```
task3_data/
└── unprocessed/
    ├── LAYING/
    │   ├── Subject_1.csv
    │   ├── Subject_2.csv
    │   └── ...
    ├── SITTING/
    ├── STANDING/
    ├── WALKING/
    ├── WALKING_DOWNSTAIRS/
    └── WALKING_UPSTAIRS/
```

Each CSV file should contain columns: `time`, `gFx`, `gFy`, `gFz`

## Output Data Structure

After running the complete pipeline, you'll have:

```
task3_data/
├── unprocessed/          # Original data (190-210Hz)
├── processed/            # Downsampled to 50Hz
├── processed_trimmed/    # 10-second windows
├── raw_dataset/          # Train/test split
│   ├── Train/
│   └── Test/
├── TSFEL_features/       # All 1173 features
├── TSFEL_dataset/        # Features with train/test split
│   ├── Train/
│   └── Test/
└── TSFEL_filtereddataset/ # Selected features only
    ├── Train/
    └── Test/
```

## Data Processing Details

### Step 1: Downsampling (190-210Hz → 50Hz)
- Uses 20ms sliding windows
- Averages accelerometer readings within each window
- Converts column names: `gFx,gFy,gFz` → `accx,accy,accz`
- Rounds values to 7 decimal places

### Step 2: Data Trimming
- Removes first 175 rows (3.5 seconds at 50Hz)
- Keeps next 500 rows (10 seconds)
- Results in clean 10-second activity windows

### Step 3-4: Train/Test Split
- Uses leave-one-subject-out strategy
- Subjects 1-8 (Nishchay & Karan) → Training set
- Subjects 9-12 (Yash) → Test set

### Step 5-6: Feature Extraction
- Extracts 1173 time-series features using TSFEL library
- Includes statistical, temporal, and spectral features
- Maintains train/test split structure

### Step 7: Feature Selection
- Applies pre-computed ANOVA F-test feature selection
- Reduces from 1173 to 58 most informative features
- Based on statistical significance testing

## Troubleshooting

### Common Issues

1. **Missing TSFEL**: Install with `pip install tsfel`
2. **Memory issues**: Process individual steps instead of complete pipeline
3. **Missing input data**: Ensure unprocessed/ directory exists with correct structure
4. **Permission errors**: Check write permissions in target directory

### Logging

The pipeline provides detailed logging. Check the console output for:
- Progress updates for each step
- File processing status
- Error messages with specific file paths
- Summary of generated datasets

## Integration with Existing Code

This pipeline generates datasets compatible with:
- `MakeTask4Dataset.py` (uses `raw_dataset/` and `processed_dataset/`)
- Existing analysis notebooks
- Machine learning experiments in the codebase

The generated datasets follow the same structure as the original task4_datacollection data, ensuring compatibility with existing analysis code.
