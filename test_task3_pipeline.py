#!/usr/bin/env python3
"""
Test script for Task 3 Data Processing Pipeline
===============================================

This script validates that the pipeline works correctly by checking:
1. Directory structure creation
2. File counts and formats
3. Data integrity across processing steps
4. Feature extraction results

Usage:
    python test_task3_pipeline.py [--base_dir path_to_task3_data]
"""

import os
import pandas as pd
import numpy as np
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Task3PipelineValidator:
    def __init__(self, base_dir="Datasets/task4_datacollection"):
        """Initialize the validator with base directory."""
        self.base_dir = base_dir
        self.activities = ['LAYING', 'SITTING', 'STANDING', 'WALKING', 'WALKING_DOWNSTAIRS', 'WALKING_UPSTAIRS']
        self.expected_subjects = [f'Subject_{i}.csv' for i in range(1, 13)]  # 12 subjects total
        
    def check_directory_structure(self):
        """Check if all expected directories exist."""
        logger.info("Checking directory structure...")
        
        expected_dirs = [
            'unprocessed',
            'processed', 
            'processed_trimmed',
            'raw_dataset',
            'raw_dataset/Train',
            'raw_dataset/Test',
            'TSFEL_features',
            'TSFEL_dataset',
            'TSFEL_dataset/Train',
            'TSFEL_dataset/Test',
            'TSFEL_filtereddataset',
            'TSFEL_filtereddataset/Train',
            'TSFEL_filtereddataset/Test'
        ]
        
        missing_dirs = []
        for dir_path in expected_dirs:
            full_path = os.path.join(self.base_dir, dir_path)
            if not os.path.exists(full_path):
                missing_dirs.append(dir_path)
                
        if missing_dirs:
            logger.error(f"Missing directories: {missing_dirs}")
            return False
        else:
            logger.info("✓ All expected directories exist")
            return True
    
    def check_activity_directories(self):
        """Check if activity directories exist in all dataset folders."""
        logger.info("Checking activity directories...")
        
        dataset_dirs = [
            'unprocessed', 'processed', 'processed_trimmed', 
            'TSFEL_features',
            'raw_dataset/Train', 'raw_dataset/Test',
            'TSFEL_dataset/Train', 'TSFEL_dataset/Test',
            'TSFEL_filtereddataset/Train', 'TSFEL_filtereddataset/Test'
        ]
        
        for dataset_dir in dataset_dirs:
            full_dataset_path = os.path.join(self.base_dir, dataset_dir)
            if not os.path.exists(full_dataset_path):
                continue
                
            for activity in self.activities:
                activity_path = os.path.join(full_dataset_path, activity)
                if not os.path.exists(activity_path):
                    logger.error(f"Missing activity directory: {dataset_dir}/{activity}")
                    return False
                    
        logger.info("✓ All activity directories exist")
        return True
    
    def check_file_counts(self):
        """Check file counts in each dataset."""
        logger.info("Checking file counts...")
        
        # Check train/test split
        train_dir = os.path.join(self.base_dir, 'raw_dataset/Train')
        test_dir = os.path.join(self.base_dir, 'raw_dataset/Test')
        
        if os.path.exists(train_dir) and os.path.exists(test_dir):
            for activity in self.activities:
                train_files = len([f for f in os.listdir(os.path.join(train_dir, activity)) if f.endswith('.csv')])
                test_files = len([f for f in os.listdir(os.path.join(test_dir, activity)) if f.endswith('.csv')])
                
                logger.info(f"{activity}: {train_files} train files, {test_files} test files")
                
                # Expected: 8 train files (subjects 1-8), 4 test files (subjects 9-12)
                if train_files != 8 or test_files != 4:
                    logger.warning(f"Unexpected file count for {activity}: expected 8 train, 4 test")
        
        logger.info("✓ File count check completed")
        return True
    
    def check_data_formats(self):
        """Check data formats and column names."""
        logger.info("Checking data formats...")
        
        # Check processed data format
        processed_dir = os.path.join(self.base_dir, 'processed')
        if os.path.exists(processed_dir):
            sample_file = None
            for activity in self.activities:
                activity_dir = os.path.join(processed_dir, activity)
                if os.path.exists(activity_dir):
                    files = [f for f in os.listdir(activity_dir) if f.endswith('.csv')]
                    if files:
                        sample_file = os.path.join(activity_dir, files[0])
                        break
            
            if sample_file:
                df = pd.read_csv(sample_file)
                expected_cols = ['accx', 'accy', 'accz']
                if list(df.columns) != expected_cols:
                    logger.error(f"Unexpected columns in processed data: {list(df.columns)}")
                    return False
                logger.info("✓ Processed data has correct format")
        
        # Check TSFEL features format
        tsfel_dir = os.path.join(self.base_dir, 'TSFEL_features')
        if os.path.exists(tsfel_dir):
            sample_file = None
            for activity in self.activities:
                activity_dir = os.path.join(tsfel_dir, activity)
                if os.path.exists(activity_dir):
                    files = [f for f in os.listdir(activity_dir) if f.endswith('.csv')]
                    if files:
                        sample_file = os.path.join(activity_dir, files[0])
                        break
            
            if sample_file:
                df = pd.read_csv(sample_file)
                logger.info(f"✓ TSFEL features: {len(df.columns)} features extracted")
                
                # Check filtered features
                filtered_dir = os.path.join(self.base_dir, 'TSFEL_filtereddataset/Train')
                if os.path.exists(filtered_dir):
                    for activity in self.activities:
                        activity_dir = os.path.join(filtered_dir, activity)
                        if os.path.exists(activity_dir):
                            files = [f for f in os.listdir(activity_dir) if f.endswith('.csv')]
                            if files:
                                filtered_file = os.path.join(activity_dir, files[0])
                                df_filtered = pd.read_csv(filtered_file)
                                logger.info(f"✓ Filtered features: {len(df_filtered.columns)} features selected")
                                break
                            break
        
        return True
    
    def check_data_integrity(self):
        """Check data integrity across processing steps."""
        logger.info("Checking data integrity...")
        
        # Check that trimmed data has expected length (500 rows for 10s at 50Hz)
        trimmed_dir = os.path.join(self.base_dir, 'processed_trimmed')
        if os.path.exists(trimmed_dir):
            for activity in self.activities:
                activity_dir = os.path.join(trimmed_dir, activity)
                if os.path.exists(activity_dir):
                    files = [f for f in os.listdir(activity_dir) if f.endswith('.csv')]
                    if files:
                        sample_file = os.path.join(activity_dir, files[0])
                        df = pd.read_csv(sample_file)
                        if len(df) != 500:
                            logger.warning(f"Unexpected length in trimmed data: {len(df)} rows (expected 500)")
                        else:
                            logger.info(f"✓ Trimmed data has correct length: {len(df)} rows")
                        break
                    break
        
        return True
    
    def run_validation(self):
        """Run complete validation suite."""
        logger.info("Starting Task 3 pipeline validation")
        logger.info(f"Base directory: {self.base_dir}")
        
        checks = [
            ("Directory structure", self.check_directory_structure),
            ("Activity directories", self.check_activity_directories),
            ("File counts", self.check_file_counts),
            ("Data formats", self.check_data_formats),
            ("Data integrity", self.check_data_integrity)
        ]
        
        all_passed = True
        for check_name, check_func in checks:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running: {check_name}")
            logger.info(f"{'='*50}")
            
            try:
                success = check_func()
                if not success:
                    logger.error(f"Check failed: {check_name}")
                    all_passed = False
            except Exception as e:
                logger.error(f"Error in check '{check_name}': {str(e)}")
                all_passed = False
        
        logger.info(f"\n{'='*50}")
        if all_passed:
            logger.info("✓ ALL VALIDATION CHECKS PASSED!")
        else:
            logger.error("✗ Some validation checks failed")
        logger.info(f"{'='*50}")
        
        return all_passed


def main():
    """Main function to run validation."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Validate Task 3 data processing pipeline results')
    parser.add_argument('--base_dir', default='Datasets/task4_datacollection',
                       help='Base directory containing the processed data (default: Datasets/task4_datacollection)')
    
    args = parser.parse_args()
    
    validator = Task3PipelineValidator(args.base_dir)
    success = validator.run_validation()
    
    if success:
        logger.info("Pipeline validation completed successfully!")
    else:
        logger.error("Pipeline validation found issues!")
        exit(1)


if __name__ == "__main__":
    main()
