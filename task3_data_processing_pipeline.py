#!/usr/bin/env python3
"""
Complete Data Processing Pipeline for Task 3 Data
==================================================

This script processes the unprocessed accelerometer data through all stages:
1. unprocessed -> processed (downsample to 50Hz)
2. processed -> processed_trimmed (remove first 4.5s and last 0.5s)
3. processed_trimmed -> raw_dataset (copy and organize for train/test split)
4. raw_dataset -> train/test split (leave-one-subject-out strategy)
5. raw_dataset -> TSFEL_features (extract all TSFEL features)
6. TSFEL_features -> TSFEL_dataset (organize with train/test splits)
7. TSFEL_dataset -> TSFEL_filtereddataset (apply feature selection)

Usage:
    python task3_data_processing_pipeline.py [--base_dir path_to_task3_data]
"""

import os
import pandas as pd
import numpy as np
import shutil
import tsfel
from datetime import timedelta
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Task3DataProcessor:
    def __init__(self, base_dir="Datasets/task4_datacollection"):
        """Initialize the data processor with base directory."""
        self.base_dir = base_dir
        self.activities = ['LAYING', 'SITTING', 'STANDING', 'WALKING', 'WALKING_DOWNSTAIRS', 'WALKING_UPSTAIRS']
        
        # Feature selection indices from ANOVA F-test (from the notebook)
        self.selected_feature_indices = np.array([
            0, 1, 3, 6, 7, 260, 298, 300, 302, 303, 304, 306, 308, 311,
            315, 319, 325, 335, 336, 339, 340, 341, 342, 343, 344, 345,
            346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 357, 358,
            359, 360, 361, 362, 363, 364, 365, 374, 679, 683, 687, 689,
            690, 715, 726, 727, 728, 736, 737, 738, 744, 1058
        ])
        
    def step1_downsample_to_50hz(self):
        """
        Step 1: Process unprocessed data to get evenly spaced samples at 50 Hz
        Input: unprocessed/ (190-210 Hz variable sampling)
        Output: processed/ (50 Hz fixed sampling)
        """
        logger.info("Step 1: Downsampling data from unprocessed to processed (50 Hz)")
        
        raw_dir = os.path.join(self.base_dir, 'unprocessed')
        processed_dir = os.path.join(self.base_dir, 'processed')
        
        if not os.path.exists(raw_dir):
            logger.error(f"Source directory {raw_dir} does not exist!")
            return False
            
        os.makedirs(processed_dir, exist_ok=True)
        
        for activity in self.activities:
            raw_activity_dir = os.path.join(raw_dir, activity)
            processed_activity_dir = os.path.join(processed_dir, activity)
            
            if not os.path.exists(raw_activity_dir):
                logger.warning(f"Activity directory {raw_activity_dir} does not exist, skipping...")
                continue
                
            os.makedirs(processed_activity_dir, exist_ok=True)
            
            for filename in os.listdir(raw_activity_dir):
                if filename.endswith('.csv'):
                    raw_filepath = os.path.join(raw_activity_dir, filename)
                    
                    try:
                        data = pd.read_csv(raw_filepath)
                        
                        # Convert time to datetime
                        data['time'] = pd.to_datetime(data['time'], format='%H:%M:%S:%f')
                        
                        # Calculate time differences and create 20ms intervals
                        start_time = data['time'].iloc[0]
                        end_time = data['time'].iloc[-1]
                        interval = timedelta(milliseconds=20)  # 20ms for 50Hz
                        
                        downsampled_data = []
                        current_time = start_time
                        
                        while current_time < end_time:
                            next_time = current_time + interval
                            
                            # Get data within this 20ms window
                            mask = (data['time'] >= current_time) & (data['time'] < next_time)
                            group = data[mask]
                            
                            if len(group) > 0:
                                avg_gFx = group['gFx'].mean()
                                avg_gFy = group['gFy'].mean()
                                avg_gFz = group['gFz'].mean()
                                downsampled_data.append([avg_gFx, avg_gFy, avg_gFz])
                            
                            current_time = next_time
                        
                        # Create DataFrame and save
                        downsampled_df = pd.DataFrame(downsampled_data, columns=['accx', 'accy', 'accz'])
                        downsampled_df = downsampled_df.round(7)
                        
                        processed_filepath = os.path.join(processed_activity_dir, filename)
                        downsampled_df.to_csv(processed_filepath, index=False)
                        
                        logger.info(f"Processed: {processed_filepath}")
                        
                    except Exception as e:
                        logger.error(f"Error processing {raw_filepath}: {str(e)}")
                        
        logger.info("Step 1 completed: Downsampling to 50Hz")
        return True
    
    def step2_trim_data(self):
        """
        Step 2: Trim data to remove first 4.5s (225 rows) and last 0.5s (25 rows)
        Input: processed/ (50 Hz)
        Output: processed_trimmed/ (10s window)
        """
        logger.info("Step 2: Trimming data to 10s window")
        
        base_dir = os.path.join(self.base_dir, 'processed')
        output_dir = os.path.join(self.base_dir, 'processed_trimmed')
        
        if not os.path.exists(base_dir):
            logger.error(f"Source directory {base_dir} does not exist!")
            return False
            
        os.makedirs(output_dir, exist_ok=True)
        
        for activity in os.listdir(base_dir):
            activity_dir = os.path.join(base_dir, activity)
            
            if os.path.isdir(activity_dir):
                output_activity_dir = os.path.join(output_dir, activity)
                os.makedirs(output_activity_dir, exist_ok=True)
                
                for filename in os.listdir(activity_dir):
                    if filename.endswith('.csv'):
                        input_filepath = os.path.join(activity_dir, filename)
                        output_filepath = os.path.join(output_activity_dir, filename)
                        
                        try:
                            data = pd.read_csv(input_filepath)
                            
                            # Remove first 175 rows (3.5s) and keep next 500 rows (10s)
                            # This gives us a 10s window from 3.5s to 13.5s
                            data_trimmed = data.iloc[175:675]
                            
                            data_trimmed.to_csv(output_filepath, index=False)
                            logger.info(f"Trimmed: {output_filepath}")
                            
                        except Exception as e:
                            logger.error(f"Error trimming {input_filepath}: {str(e)}")
                            
        logger.info("Step 2 completed: Data trimming")
        return True

    def step3_create_raw_dataset(self):
        """
        Step 3: Copy processed_trimmed to raw_dataset and organize for train/test split
        Input: processed_trimmed/
        Output: raw_dataset/Train/ (copy of all data)
        """
        logger.info("Step 3: Creating raw_dataset structure")

        source_dir = os.path.join(self.base_dir, 'processed_trimmed')
        target_dir = os.path.join(self.base_dir, 'raw_dataset')
        train_dir = os.path.join(target_dir, 'Train')

        if not os.path.exists(source_dir):
            logger.error(f"Source directory {source_dir} does not exist!")
            return False

        # Remove existing raw_dataset if it exists
        if os.path.exists(target_dir):
            shutil.rmtree(target_dir)

        os.makedirs(train_dir, exist_ok=True)

        # Copy all data to Train directory
        for activity in self.activities:
            source_activity_dir = os.path.join(source_dir, activity)
            target_activity_dir = os.path.join(train_dir, activity)

            if os.path.exists(source_activity_dir):
                shutil.copytree(source_activity_dir, target_activity_dir)
                logger.info(f"Copied {activity} data to raw_dataset/Train/")
            else:
                logger.warning(f"Activity directory {source_activity_dir} does not exist")

        logger.info("Step 3 completed: Raw dataset structure created")
        return True

    def step4_train_test_split(self):
        """
        Step 4: Split data using leave-one-subject-out strategy
        Subjects 9-12 (Yash) go to Test, others stay in Train
        """
        logger.info("Step 4: Creating train/test split")

        base_dir = os.path.join(self.base_dir, 'raw_dataset')
        train_dir = os.path.join(base_dir, 'Train')
        test_dir = os.path.join(base_dir, 'Test')

        if not os.path.exists(train_dir):
            logger.error(f"Train directory {train_dir} does not exist!")
            return False

        # Files to move to test (Yash's data - subjects 9-12)
        files_to_move = ['Subject_9.csv', 'Subject_10.csv', 'Subject_11.csv', 'Subject_12.csv']

        for activity in self.activities:
            activity_train_dir = os.path.join(train_dir, activity)
            activity_test_dir = os.path.join(test_dir, activity)

            if not os.path.exists(activity_train_dir):
                logger.warning(f"Activity train directory {activity_train_dir} does not exist")
                continue

            os.makedirs(activity_test_dir, exist_ok=True)

            # Move specified subject files from Train to Test
            for file_name in files_to_move:
                train_file_path = os.path.join(activity_train_dir, file_name)
                test_file_path = os.path.join(activity_test_dir, file_name)

                if os.path.exists(train_file_path):
                    shutil.move(train_file_path, test_file_path)
                    logger.info(f"Moved {file_name} from Train to Test for {activity}")

        logger.info("Step 4 completed: Train/test split")
        return True

    def step5_extract_tsfel_features(self):
        """
        Step 5: Extract TSFEL features from raw_dataset
        Input: raw_dataset/Train/ and raw_dataset/Test/
        Output: TSFEL_features/ (all features, no train/test split)
        """
        logger.info("Step 5: Extracting TSFEL features")

        source_base = os.path.join(self.base_dir, 'raw_dataset')
        output_dir = os.path.join(self.base_dir, 'TSFEL_features')

        if not os.path.exists(source_base):
            logger.error(f"Source directory {source_base} does not exist!")
            return False

        # Remove existing TSFEL_features if it exists
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)

        # Configure TSFEL to use all features
        cfg = tsfel.get_features_by_domain()
        for domain in cfg:
            for feature in cfg[domain]:
                cfg[domain][feature]['use'] = 'yes'  # Enable all features

        # Process both Train and Test data into a single features directory
        for split in ['Train', 'Test']:
            split_dir = os.path.join(source_base, split)
            if not os.path.exists(split_dir):
                continue

            for activity in self.activities:
                activity_dir = os.path.join(split_dir, activity)
                output_activity_dir = os.path.join(output_dir, activity)

                if not os.path.exists(activity_dir):
                    continue

                Path(output_activity_dir).mkdir(parents=True, exist_ok=True)

                subject_files = [f for f in os.listdir(activity_dir) if f.endswith('.csv')]
                for file in subject_files:
                    file_path = os.path.join(activity_dir, file)

                    try:
                        df = pd.read_csv(file_path)

                        # Extract features using TSFEL
                        features = tsfel.time_series_features_extractor(cfg, df, fs=50)

                        subject_id = file.split('.')[0]
                        output_file = os.path.join(output_activity_dir, f'{subject_id}.csv')
                        features.to_csv(output_file, index=False)

                        logger.info(f"Extracted features for {activity}/{subject_id}")

                    except Exception as e:
                        logger.error(f"Error extracting features from {file_path}: {str(e)}")

        logger.info("Step 5 completed: TSFEL feature extraction")
        return True

    def step6_create_tsfel_dataset(self):
        """
        Step 6: Organize TSFEL features with train/test splits
        Input: raw_dataset/ structure + TSFEL_features/
        Output: TSFEL_dataset/Train/ and TSFEL_dataset/Test/
        """
        logger.info("Step 6: Creating TSFEL dataset with train/test splits")

        features_dir = os.path.join(self.base_dir, 'TSFEL_features')
        raw_dataset_dir = os.path.join(self.base_dir, 'raw_dataset')
        output_dir = os.path.join(self.base_dir, 'TSFEL_dataset')

        if not os.path.exists(features_dir) or not os.path.exists(raw_dataset_dir):
            logger.error("Required source directories do not exist!")
            return False

        # Remove existing TSFEL_dataset if it exists
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)

        # Create train and test directories
        for split in ['Train', 'Test']:
            split_source = os.path.join(raw_dataset_dir, split)
            split_output = os.path.join(output_dir, split)

            if not os.path.exists(split_source):
                continue

            for activity in self.activities:
                activity_source = os.path.join(split_source, activity)
                activity_output = os.path.join(split_output, activity)
                features_source = os.path.join(features_dir, activity)

                if not os.path.exists(activity_source) or not os.path.exists(features_source):
                    continue

                Path(activity_output).mkdir(parents=True, exist_ok=True)

                # Copy corresponding feature files
                subject_files = [f for f in os.listdir(activity_source) if f.endswith('.csv')]
                for file in subject_files:
                    feature_file = os.path.join(features_source, file)
                    output_file = os.path.join(activity_output, file)

                    if os.path.exists(feature_file):
                        shutil.copy2(feature_file, output_file)
                        logger.info(f"Copied features for {split}/{activity}/{file}")

        logger.info("Step 6 completed: TSFEL dataset with splits")
        return True

    def step7_filter_tsfel_features(self):
        """
        Step 7: Apply feature selection to TSFEL dataset
        Input: TSFEL_dataset/
        Output: TSFEL_filtereddataset/ (selected features only)
        """
        logger.info("Step 7: Filtering TSFEL features using ANOVA F-test selection")

        original_dir = os.path.join(self.base_dir, 'TSFEL_dataset')
        filtered_dir = os.path.join(self.base_dir, 'TSFEL_filtereddataset')

        if not os.path.exists(original_dir):
            logger.error(f"Source directory {original_dir} does not exist!")
            return False

        # Remove existing filtered dataset if it exists
        if os.path.exists(filtered_dir):
            shutil.rmtree(filtered_dir)

        # Copy the entire directory structure
        shutil.copytree(original_dir, filtered_dir)

        feature_names = None

        # Apply feature selection to all CSV files
        for root, _, files in os.walk(filtered_dir):
            for file in files:
                if file.endswith('.csv'):
                    file_path = os.path.join(root, file)

                    try:
                        df = pd.read_csv(file_path)

                        # Store feature names from first file
                        if feature_names is None:
                            feature_names = df.columns.tolist()

                        # Apply feature selection
                        df_filtered = df.iloc[:, self.selected_feature_indices]
                        df_filtered.to_csv(file_path, index=False)

                        logger.info(f"Filtered features for {file_path}")

                    except Exception as e:
                        logger.error(f"Error filtering {file_path}: {str(e)}")

        if feature_names is not None:
            logger.info(f"Applied feature selection: {len(self.selected_feature_indices)} features selected from {len(feature_names)} total features")

        logger.info("Step 7 completed: Feature filtering")
        return True

    def run_complete_pipeline(self):
        """Run the complete data processing pipeline."""
        logger.info("Starting complete Task 3 data processing pipeline")
        logger.info(f"Base directory: {self.base_dir}")

        steps = [
            ("Downsample to 50Hz", self.step1_downsample_to_50hz),
            ("Trim data to 10s window", self.step2_trim_data),
            ("Create raw dataset structure", self.step3_create_raw_dataset),
            ("Create train/test split", self.step4_train_test_split),
            ("Extract TSFEL features", self.step5_extract_tsfel_features),
            ("Create TSFEL dataset with splits", self.step6_create_tsfel_dataset),
            ("Filter TSFEL features", self.step7_filter_tsfel_features)
        ]

        for step_name, step_func in steps:
            logger.info(f"\n{'='*60}")
            logger.info(f"Starting: {step_name}")
            logger.info(f"{'='*60}")

            try:
                success = step_func()
                if not success:
                    logger.error(f"Step failed: {step_name}")
                    return False
                logger.info(f"Completed: {step_name}")
            except Exception as e:
                logger.error(f"Error in step '{step_name}': {str(e)}")
                return False

        logger.info(f"\n{'='*60}")
        logger.info("PIPELINE COMPLETED SUCCESSFULLY!")
        logger.info(f"{'='*60}")
        logger.info("Generated datasets:")
        logger.info("- processed/ (50Hz downsampled)")
        logger.info("- processed_trimmed/ (10s window)")
        logger.info("- raw_dataset/ (train/test split)")
        logger.info("- TSFEL_features/ (all 1173 features)")
        logger.info("- TSFEL_dataset/ (features with train/test split)")
        logger.info("- TSFEL_filtereddataset/ (selected features)")

        return True


def main():
    """Main function to run the pipeline."""
    import argparse

    parser = argparse.ArgumentParser(description='Process Task 3 accelerometer data through complete pipeline')
    parser.add_argument('--base_dir', default='Datasets/task4_datacollection',
                       help='Base directory containing the unprocessed data (default: Datasets/task4_datacollection)')
    parser.add_argument('--step', type=int, choices=range(1, 8),
                       help='Run only a specific step (1-7). If not specified, runs complete pipeline.')

    args = parser.parse_args()

    processor = Task3DataProcessor(args.base_dir)

    if args.step:
        step_functions = {
            1: processor.step1_downsample_to_50hz,
            2: processor.step2_trim_data,
            3: processor.step3_create_raw_dataset,
            4: processor.step4_train_test_split,
            5: processor.step5_extract_tsfel_features,
            6: processor.step6_create_tsfel_dataset,
            7: processor.step7_filter_tsfel_features
        }

        logger.info(f"Running step {args.step} only")
        success = step_functions[args.step]()
        if success:
            logger.info(f"Step {args.step} completed successfully")
        else:
            logger.error(f"Step {args.step} failed")
    else:
        processor.run_complete_pipeline()


if __name__ == "__main__":
    main()
